# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/cv_scoring_db

# JWT Configuration
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# HRFlow API Configuration
HRFLOW_API_KEY=your-hrflow-api-key
HRFLOW_API_URL=https://api.hrflow.ai/v1

# OpenAI Configuration (for scoring)
OPENAI_API_KEY=your-openai-api-key

# Redis Configuration (for Celery)
REDIS_URL=redis://localhost:6379/0

# File Upload Configuration
MAX_FILE_SIZE_MB=10
ALLOWED_FILE_TYPES=pdf,doc,docx
UPLOAD_DIR=./uploads

# Security Configuration
RATE_LIMIT_UPLOADS_PER_HOUR=50
ENABLE_PROMPT_INJECTION_DETECTION=true

# Environment
ENVIRONMENT=development
DEBUG=true
