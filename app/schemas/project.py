from pydantic import BaseModel
from datetime import datetime
from typing import Optional, Dict, Any, List


class JobRequirements(BaseModel):
    required_education: Optional[str] = None
    preferred_fields: List[str] = []
    min_experience_years: Optional[int] = None
    preferred_experience_years: Optional[int] = None
    required_skills: List[str] = []
    preferred_skills: List[str] = []
    technical_requirements: Dict[str, List[str]] = {}
    required_languages: List[str] = []
    location_requirements: Optional[str] = None
    remote_work: bool = False


class ScoringConfig(BaseModel):
    weights: Dict[str, float] = {
        "education_relevance": 0.25,
        "skills_match": 0.25,
        "experience_quality": 0.20,
        "technical_proficiency": 0.15,
        "career_progression": 0.10,
        "language_fit": 0.05
    }


class ProjectBase(BaseModel):
    title: str
    description: Optional[str] = None
    job_title: Optional[str] = None
    company: Optional[str] = None
    location: Optional[str] = None
    job_description: Optional[str] = None
    job_requirements: Optional[JobRequirements] = None
    scoring_config: Optional[ScoringConfig] = None


class ProjectCreate(ProjectBase):
    pass


class ProjectUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    job_title: Optional[str] = None
    company: Optional[str] = None
    location: Optional[str] = None
    job_description: Optional[str] = None
    job_requirements: Optional[JobRequirements] = None
    scoring_config: Optional[ScoringConfig] = None
    status: Optional[str] = None


class ProjectInDB(ProjectBase):
    id: int
    status: str
    owner_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class Project(ProjectInDB):
    candidate_count: int = 0
    selected_candidates_count: int = 0
