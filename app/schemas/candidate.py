from pydantic import BaseModel
from datetime import datetime
from typing import Optional, Dict, Any, List


class PersonalInfo(BaseModel):
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    location: Optional[str] = None


class Education(BaseModel):
    degree: Optional[str] = None
    field: Optional[str] = None
    institution: Optional[str] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None


class Experience(BaseModel):
    title: Optional[str] = None
    company: Optional[str] = None
    duration: Optional[str] = None
    description: Optional[str] = None
    skills_used: List[str] = []


class ParsedData(BaseModel):
    personal_info: Optional[PersonalInfo] = None
    education: List[Education] = []
    experience: List[Experience] = []
    skills: List[str] = []
    languages: List[str] = []
    certifications: List[str] = []


class ScoreDetail(BaseModel):
    score: float
    reasoning: str


class ScoringResults(BaseModel):
    scores: Dict[str, ScoreDetail] = {}
    final_score: float
    recommendation: str
    confidence_level: str
    flags: List[str] = []
    scored_at: datetime


class CandidateBase(BaseModel):
    cv_filename: str
    original_filename: str


class CandidateCreate(CandidateBase):
    pass


class CandidateUpdate(BaseModel):
    selected: Optional[bool] = None
    selection_notes: Optional[str] = None


class CandidateInDB(CandidateBase):
    id: int
    project_id: int
    file_size: Optional[int] = None
    hrflow_profile_id: Optional[str] = None
    parsed_data: Optional[ParsedData] = None
    scoring_results: Optional[ScoringResults] = None
    final_score: Optional[float] = None
    confidence_level: Optional[str] = None
    selected: bool = False
    selection_notes: Optional[str] = None
    processing_status: str
    error_message: Optional[str] = None
    uploaded_at: datetime
    processed_at: Optional[datetime] = None
    scored_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class Candidate(CandidateInDB):
    pass


class CandidateList(BaseModel):
    id: int
    original_filename: str
    final_score: Optional[float] = None
    confidence_level: Optional[str] = None
    selected: bool = False
    processing_status: str
    uploaded_at: datetime
    personal_info: Optional[PersonalInfo] = None


class BulkSelectRequest(BaseModel):
    candidate_ids: List[int]
    selected: bool
