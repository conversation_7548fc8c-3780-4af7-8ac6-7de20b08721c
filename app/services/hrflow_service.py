import httpx
import json
from typing import Dict, Any, Optional
from fastapi import HTTPEx<PERSON>, status

from app.core.config import settings


class HRFlowService:
    def __init__(self):
        self.api_key = settings.HRFLOW_API_KEY
        self.base_url = settings.HRFLOW_API_URL
        self.headers = {
            "X-API-KEY": self.api_key,
            "Content-Type": "application/json"
        }

    async def parse_cv(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """
        Parse CV using HRFlow API
        """
        if not self.api_key:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="HRFlow API key not configured"
            )

        try:
            async with httpx.AsyncClient() as client:
                # Prepare the file for upload
                files = {
                    "file": (filename, file_content, "application/octet-stream")
                }
                
                # HRFlow parsing endpoint
                url = f"{self.base_url}/profile/parsing/file"
                
                response = await client.post(
                    url,
                    headers={"X-API-KEY": self.api_key},
                    files=files,
                    timeout=30.0
                )
                
                if response.status_code != 200:
                    raise HTTPException(
                        status_code=status.HTTP_502_BAD_GATEWAY,
                        detail=f"HRFlow API error: {response.status_code}"
                    )
                
                result = response.json()
                
                # Validate response structure
                if not result.get("data"):
                    raise HTTPException(
                        status_code=status.HTTP_502_BAD_GATEWAY,
                        detail="Invalid response from HRFlow API"
                    )
                
                return result
                
        except httpx.TimeoutException:
            raise HTTPException(
                status_code=status.HTTP_504_GATEWAY_TIMEOUT,
                detail="HRFlow API timeout"
            )
        except httpx.RequestError as e:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail=f"HRFlow API connection error: {str(e)}"
            )

    def extract_structured_data(self, hrflow_response: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract and structure candidate data from HRFlow response
        """
        try:
            profile_data = hrflow_response.get("data", {})
            
            # Extract personal information
            personal_info = {
                "name": profile_data.get("info", {}).get("full_name"),
                "email": profile_data.get("info", {}).get("email"),
                "phone": profile_data.get("info", {}).get("phone"),
                "location": profile_data.get("info", {}).get("location", {}).get("text")
            }
            
            # Extract education
            education = []
            for edu in profile_data.get("educations", []):
                education.append({
                    "degree": edu.get("title"),
                    "field": edu.get("description"),
                    "institution": edu.get("school"),
                    "start_date": edu.get("date_start"),
                    "end_date": edu.get("date_end")
                })
            
            # Extract experience
            experience = []
            for exp in profile_data.get("experiences", []):
                experience.append({
                    "title": exp.get("title"),
                    "company": exp.get("company"),
                    "duration": f"{exp.get('date_start', '')} - {exp.get('date_end', '')}",
                    "description": exp.get("description"),
                    "skills_used": [skill.get("name") for skill in exp.get("skills", [])]
                })
            
            # Extract skills
            skills = [skill.get("name") for skill in profile_data.get("skills", [])]
            
            # Extract languages
            languages = [lang.get("name") for lang in profile_data.get("languages", [])]
            
            # Extract certifications (if available)
            certifications = []
            for cert in profile_data.get("certifications", []):
                certifications.append(cert.get("name"))
            
            return {
                "personal_info": personal_info,
                "education": education,
                "experience": experience,
                "skills": skills,
                "languages": languages,
                "certifications": certifications
            }
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error processing HRFlow response: {str(e)}"
            )
