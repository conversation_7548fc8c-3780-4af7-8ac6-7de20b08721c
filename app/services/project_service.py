from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func

from app.models.project import Project
from app.models.candidate import Candidate
from app.schemas.project import ProjectCreate, ProjectUpdate, ScoringConfig


class ProjectService:
    def __init__(self, db: Session):
        self.db = db

    def create_project(self, project_data: ProjectCreate, owner_id: int) -> Project:
        """Create a new project"""
        # Set default scoring config if not provided
        scoring_config = project_data.scoring_config
        if not scoring_config:
            scoring_config = ScoringConfig()

        db_project = Project(
            title=project_data.title,
            description=project_data.description,
            job_title=project_data.job_title,
            company=project_data.company,
            location=project_data.location,
            job_description=project_data.job_description,
            job_requirements=project_data.job_requirements.dict() if project_data.job_requirements else None,
            scoring_config=scoring_config.dict(),
            owner_id=owner_id,
            status="active"
        )
        self.db.add(db_project)
        self.db.commit()
        self.db.refresh(db_project)
        return db_project

    def get_project(self, project_id: int, owner_id: int) -> Optional[Project]:
        """Get a project by ID for a specific owner"""
        return self.db.query(Project).filter(
            Project.id == project_id,
            Project.owner_id == owner_id
        ).first()

    def get_user_projects(self, owner_id: int, skip: int = 0, limit: int = 100) -> List[Project]:
        """Get all projects for a user with candidate counts"""
        projects = self.db.query(Project).filter(
            Project.owner_id == owner_id
        ).offset(skip).limit(limit).all()

        # Add candidate counts
        for project in projects:
            candidate_count = self.db.query(func.count(Candidate.id)).filter(
                Candidate.project_id == project.id
            ).scalar()
            
            selected_count = self.db.query(func.count(Candidate.id)).filter(
                Candidate.project_id == project.id,
                Candidate.selected == True
            ).scalar()
            
            # Add these as dynamic attributes
            project.candidate_count = candidate_count or 0
            project.selected_candidates_count = selected_count or 0

        return projects

    def update_project(self, project_id: int, project_update: ProjectUpdate, owner_id: int) -> Optional[Project]:
        """Update a project"""
        project = self.get_project(project_id, owner_id)
        if not project:
            return None

        update_data = project_update.dict(exclude_unset=True)
        
        # Handle nested objects
        if "job_requirements" in update_data and update_data["job_requirements"]:
            update_data["job_requirements"] = update_data["job_requirements"].dict()
        
        if "scoring_config" in update_data and update_data["scoring_config"]:
            update_data["scoring_config"] = update_data["scoring_config"].dict()

        for field, value in update_data.items():
            setattr(project, field, value)

        self.db.commit()
        self.db.refresh(project)
        return project

    def archive_project(self, project_id: int, owner_id: int) -> bool:
        """Archive a project (soft delete)"""
        project = self.get_project(project_id, owner_id)
        if not project:
            return False

        project.status = "archived"
        self.db.commit()
        return True

    def delete_project(self, project_id: int, owner_id: int) -> bool:
        """Permanently delete a project"""
        project = self.get_project(project_id, owner_id)
        if not project:
            return False

        self.db.delete(project)
        self.db.commit()
        return True
