import os
import uuid
import magic
from typing import List, Tuple
from fastapi import HTT<PERSON>Exception, status, UploadFile

from app.core.config import settings


class FileService:
    def __init__(self):
        self.upload_dir = settings.UPLOAD_DIR
        self.max_file_size = settings.max_file_size_bytes
        self.allowed_extensions = settings.allowed_file_extensions
        
        # Create upload directory if it doesn't exist
        os.makedirs(self.upload_dir, exist_ok=True)

    def validate_file(self, file: UploadFile) -> None:
        """
        Validate uploaded file
        """
        # Check file size
        if file.size and file.size > self.max_file_size:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File size exceeds maximum allowed size of {settings.MAX_FILE_SIZE_MB}MB"
            )
        
        # Check file extension
        if file.filename:
            file_extension = file.filename.split('.')[-1].lower()
            if file_extension not in self.allowed_extensions:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"File type not allowed. Allowed types: {', '.join(self.allowed_extensions)}"
                )

    def validate_file_content(self, file_content: bytes, filename: str) -> None:
        """
        Validate file content using python-magic
        """
        try:
            mime_type = magic.from_buffer(file_content, mime=True)
            
            # Define allowed MIME types
            allowed_mime_types = {
                'pdf': 'application/pdf',
                'doc': 'application/msword',
                'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            }
            
            file_extension = filename.split('.')[-1].lower()
            expected_mime = allowed_mime_types.get(file_extension)
            
            if expected_mime and mime_type != expected_mime:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"File content doesn't match extension. Expected {expected_mime}, got {mime_type}"
                )
                
        except Exception as e:
            # If magic fails, we'll allow the file but log the error
            print(f"File validation warning: {str(e)}")

    async def save_temporary_file(self, file: UploadFile) -> Tuple[str, bytes]:
        """
        Save file temporarily and return file path and content
        """
        # Validate file
        self.validate_file(file)
        
        # Read file content
        file_content = await file.read()
        
        # Validate file content
        self.validate_file_content(file_content, file.filename)
        
        # Generate unique filename
        file_extension = file.filename.split('.')[-1].lower()
        unique_filename = f"{uuid.uuid4()}.{file_extension}"
        file_path = os.path.join(self.upload_dir, unique_filename)
        
        # Save file temporarily
        with open(file_path, "wb") as buffer:
            buffer.write(file_content)
        
        return file_path, file_content

    def cleanup_file(self, file_path: str) -> None:
        """
        Remove temporary file
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception as e:
            print(f"Error cleaning up file {file_path}: {str(e)}")

    async def process_multiple_files(self, files: List[UploadFile]) -> List[Tuple[str, str, bytes]]:
        """
        Process multiple files and return list of (original_filename, temp_path, content)
        """
        if len(files) > 20:  # Reasonable limit
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Too many files. Maximum 20 files allowed per upload."
            )
        
        processed_files = []
        
        for file in files:
            try:
                temp_path, content = await self.save_temporary_file(file)
                processed_files.append((file.filename, temp_path, content))
            except Exception as e:
                # Cleanup any files that were already processed
                for _, temp_path, _ in processed_files:
                    self.cleanup_file(temp_path)
                raise e
        
        return processed_files
