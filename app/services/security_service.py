import re
import bleach
from typing import Dict, Any, List
from fastapi import HTTPException, status

from app.core.config import settings


class SecurityService:
    def __init__(self):
        self.prompt_injection_patterns = [
            r'ignore\s+previous\s+instructions',
            r'forget\s+everything',
            r'system\s*:',
            r'assistant\s*:',
            r'human\s*:',
            r'<\s*script\s*>',
            r'javascript\s*:',
            r'eval\s*\(',
            r'exec\s*\(',
            r'__import__',
            r'subprocess',
            r'os\.system',
            r'shell\s*=\s*true',
        ]
        
        # Compile patterns for efficiency
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.prompt_injection_patterns]

    def detect_prompt_injection(self, text: str) -> bool:
        """
        Detect potential prompt injection attempts
        """
        if not settings.ENABLE_PROMPT_INJECTION_DETECTION:
            return False
            
        if not text:
            return False
            
        # Check for suspicious patterns
        for pattern in self.compiled_patterns:
            if pattern.search(text):
                return True
        
        # Check for excessive special characters
        special_char_ratio = sum(1 for c in text if not c.isalnum() and not c.isspace()) / len(text)
        if special_char_ratio > 0.3:  # More than 30% special characters
            return True
            
        return False

    def sanitize_text(self, text: str) -> str:
        """
        Sanitize text content
        """
        if not text:
            return text
            
        # Remove HTML tags and potentially dangerous content
        cleaned_text = bleach.clean(
            text,
            tags=[],  # No HTML tags allowed
            attributes={},
            strip=True
        )
        
        # Remove excessive whitespace
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
        
        # Limit length
        if len(cleaned_text) > 10000:  # 10k character limit
            cleaned_text = cleaned_text[:10000] + "..."
            
        return cleaned_text

    def sanitize_candidate_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitize candidate data from HRFlow response
        """
        if not data:
            return data
            
        sanitized = {}
        
        # Sanitize personal info
        if "personal_info" in data:
            personal_info = data["personal_info"]
            sanitized["personal_info"] = {
                "name": self._sanitize_field(personal_info.get("name")),
                "email": self._sanitize_email(personal_info.get("email")),
                "phone": self._sanitize_field(personal_info.get("phone")),
                "location": self._sanitize_field(personal_info.get("location"))
            }
        
        # Sanitize education
        if "education" in data:
            sanitized["education"] = []
            for edu in data["education"]:
                sanitized["education"].append({
                    "degree": self._sanitize_field(edu.get("degree")),
                    "field": self._sanitize_field(edu.get("field")),
                    "institution": self._sanitize_field(edu.get("institution")),
                    "start_date": self._sanitize_field(edu.get("start_date")),
                    "end_date": self._sanitize_field(edu.get("end_date"))
                })
        
        # Sanitize experience
        if "experience" in data:
            sanitized["experience"] = []
            for exp in data["experience"]:
                sanitized["experience"].append({
                    "title": self._sanitize_field(exp.get("title")),
                    "company": self._sanitize_field(exp.get("company")),
                    "duration": self._sanitize_field(exp.get("duration")),
                    "description": self._sanitize_long_text(exp.get("description")),
                    "skills_used": [self._sanitize_field(skill) for skill in exp.get("skills_used", [])]
                })
        
        # Sanitize skills
        if "skills" in data:
            sanitized["skills"] = [self._sanitize_field(skill) for skill in data["skills"]]
        
        # Sanitize languages
        if "languages" in data:
            sanitized["languages"] = [self._sanitize_field(lang) for lang in data["languages"]]
        
        # Sanitize certifications
        if "certifications" in data:
            sanitized["certifications"] = [self._sanitize_field(cert) for cert in data["certifications"]]
        
        return sanitized

    def _sanitize_field(self, field: str) -> str:
        """Sanitize a short text field"""
        if not field:
            return field
            
        # Check for prompt injection
        if self.detect_prompt_injection(field):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Suspicious content detected in CV data"
            )
        
        return self.sanitize_text(field)

    def _sanitize_long_text(self, text: str) -> str:
        """Sanitize longer text fields like descriptions"""
        if not text:
            return text
            
        # Check for prompt injection
        if self.detect_prompt_injection(text):
            # For longer text, we might want to just clean it rather than reject
            # Log the incident for monitoring
            print(f"Potential prompt injection detected in long text: {text[:100]}...")
        
        return self.sanitize_text(text)

    def _sanitize_email(self, email: str) -> str:
        """Sanitize email field with additional validation"""
        if not email:
            return email
            
        # Basic email validation
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            return ""  # Invalid email, return empty string
            
        return self._sanitize_field(email)

    def validate_job_description(self, job_description: str) -> str:
        """
        Validate and sanitize job description
        """
        if not job_description:
            return job_description
            
        # Check for prompt injection
        if self.detect_prompt_injection(job_description):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Suspicious content detected in job description"
            )
        
        return self.sanitize_text(job_description)
