from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.schemas.candidate import Candidate, CandidateUpdate, BulkSelectRequest
from app.services.candidate_service import CandidateService

router = APIRouter()


@router.get("/{candidate_id}", response_model=Candidate)
async def get_candidate(
    candidate_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get detailed candidate information
    """
    candidate_service = CandidateService(db)
    candidate = candidate_service.get_candidate(candidate_id, current_user.id)
    if not candidate:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Candidate not found"
        )
    return candidate


@router.put("/{candidate_id}/select")
async def select_candidate(
    candidate_id: int,
    selection_data: CandidateUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Select/deselect candidate for contact
    """
    candidate_service = CandidateService(db)
    candidate = candidate_service.update_candidate_selection(
        candidate_id, selection_data, current_user.id
    )
    if not candidate:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Candidate not found"
        )
    return {"success": True, "candidate": candidate}


@router.post("/bulk-select")
async def bulk_select_candidates(
    selection_data: BulkSelectRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Bulk select/deselect candidates
    """
    candidate_service = CandidateService(db)
    result = candidate_service.bulk_select_candidates(
        selection_data.candidate_ids, selection_data.selected, current_user.id
    )
    return result
