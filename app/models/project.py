from sqlalchemy import Column, Inte<PERSON>, <PERSON>, Date<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, JSON, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from app.core.database import Base


class Project(Base):
    __tablename__ = "projects"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String, nullable=False)
    description = Column(Text)
    status = Column(String, default="active")  # active, completed, archived
    
    # Job description fields
    job_title = Column(String)
    company = Column(String)
    location = Column(String)
    job_description = Column(Text)
    job_requirements = Column(JSON)  # Structured requirements
    
    # Scoring configuration
    scoring_config = Column(JSON)  # Weights and parameters
    
    # Metadata
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    owner = relationship("User", back_populates="projects")
    candidates = relationship("Candidate", back_populates="project", cascade="all, delete-orphan")
