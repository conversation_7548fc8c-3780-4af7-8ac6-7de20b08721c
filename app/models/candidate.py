from sqlalchemy import Column, Integer, String, DateTime, <PERSON>olean, Foreign<PERSON>ey, JSON, Text, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from app.core.database import Base


class Candidate(Base):
    __tablename__ = "candidates"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    
    # File information
    cv_filename = Column(String, nullable=False)
    original_filename = Column(String, nullable=False)
    file_size = Column(Integer)
    
    # HRFlow integration
    hrflow_profile_id = Column(String)
    hrflow_response = Column(JSON)  # Raw HRFlow response
    
    # Parsed candidate data
    parsed_data = Column(JSON)  # Structured candidate information
    
    # Scoring results
    scoring_results = Column(JSON)  # Detailed scoring breakdown
    final_score = Column(Float)
    confidence_level = Column(String)
    
    # Selection status
    selected = Column(Boolean, default=False)
    selection_notes = Column(Text)
    
    # Processing status
    processing_status = Column(String, default="pending")  # pending, processing, completed, failed
    error_message = Column(Text)
    
    # Metadata
    uploaded_at = Column(DateTime(timezone=True), server_default=func.now())
    processed_at = Column(DateTime(timezone=True))
    scored_at = Column(DateTime(timezone=True))

    # Relationships
    project = relationship("Project", back_populates="candidates")
