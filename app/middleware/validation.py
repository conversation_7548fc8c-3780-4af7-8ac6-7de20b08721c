import json
from fastapi import Request, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware

from app.services.security_service import SecurityService


class ValidationMiddleware(BaseHTTPMiddleware):
    def __init__(self, app):
        super().__init__(app)
        self.security_service = SecurityService()

    async def dispatch(self, request: Request, call_next):
        # Validate request body for potential security issues
        if request.method in ["POST", "PUT", "PATCH"]:
            await self._validate_request_body(request)
        
        response = await call_next(request)
        return response

    async def _validate_request_body(self, request: Request):
        """Validate request body for security issues"""
        try:
            # Read request body
            body = await request.body()
            
            if not body:
                return
            
            # Try to parse as JSON
            try:
                data = json.loads(body.decode())
                self._validate_json_data(data)
            except json.JSONDecodeError:
                # Not JSON, validate as text
                text_data = body.decode()
                if self.security_service.detect_prompt_injection(text_data):
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Suspicious content detected in request"
                    )
            
            # Reset request body for downstream processing
            request._body = body
            
        except UnicodeDecodeError:
            # Binary data, skip validation
            pass

    def _validate_json_data(self, data):
        """Recursively validate JSON data"""
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, str):
                    if self.security_service.detect_prompt_injection(value):
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail=f"Suspicious content detected in field: {key}"
                        )
                elif isinstance(value, (dict, list)):
                    self._validate_json_data(value)
        elif isinstance(data, list):
            for item in data:
                if isinstance(item, str):
                    if self.security_service.detect_prompt_injection(item):
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail="Suspicious content detected in request"
                        )
                elif isinstance(item, (dict, list)):
                    self._validate_json_data(item)
