import time
from typing import Dict, Optional
from fastapi import Request, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response

from app.core.config import settings


class RateLimitMiddleware(BaseHTTPMiddleware):
    def __init__(self, app, calls_per_hour: int = 50):
        super().__init__(app)
        self.calls_per_hour = calls_per_hour
        self.window_size = 3600  # 1 hour in seconds
        self.user_requests: Dict[str, list] = {}

    async def dispatch(self, request: Request, call_next):
        # Only apply rate limiting to upload endpoints
        if "/candidates" in str(request.url) and request.method == "POST":
            user_id = self._get_user_id(request)
            
            if user_id and self._is_rate_limited(user_id):
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail=f"Rate limit exceeded. Maximum {self.calls_per_hour} uploads per hour."
                )
        
        response = await call_next(request)
        return response

    def _get_user_id(self, request: Request) -> Optional[str]:
        """Extract user ID from request (simplified)"""
        # In a real implementation, you'd extract this from the JWT token
        # For now, use IP address as a fallback
        return request.client.host if request.client else None

    def _is_rate_limited(self, user_id: str) -> bool:
        """Check if user has exceeded rate limit"""
        current_time = time.time()
        
        # Initialize user request history if not exists
        if user_id not in self.user_requests:
            self.user_requests[user_id] = []
        
        # Remove old requests outside the window
        self.user_requests[user_id] = [
            req_time for req_time in self.user_requests[user_id]
            if current_time - req_time < self.window_size
        ]
        
        # Check if limit exceeded
        if len(self.user_requests[user_id]) >= self.calls_per_hour:
            return True
        
        # Add current request
        self.user_requests[user_id].append(current_time)
        return False
